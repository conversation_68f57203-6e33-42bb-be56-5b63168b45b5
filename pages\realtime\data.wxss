/* pages/realtime/data.wxss */

/* ==================== 页面容器 ==================== */
.realtime-data-page {
  min-height: 100vh;
  background-color: var(--van-background-color);
}

/* ==================== 页面头部 ==================== */
.header-section {
  background-color: var(--van-background-2);
  padding: var(--spacing-md);
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: var(--shadow-light);
}

.device-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.device-name {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--van-text-color);
}

.device-id {
  font-size: var(--font-size-sm);
  color: var(--van-text-color-2);
}

.update-time {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.time-text {
  font-size: var(--font-size-sm);
  color: var(--van-text-color-3);
}

/* ==================== 标签页内容 ==================== */
.tab-content {
  padding: var(--spacing-md);
  min-height: 60vh;
}

/* ==================== 加载状态 ==================== */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: var(--spacing-xxl) 0;
}

/* ==================== 错误状态 ==================== */
.error-container {
  padding: var(--spacing-xxl) 0;
}

/* ==================== 参数列表 ==================== */
.params-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.params-group {
  border-radius: var(--border-radius-lg) !important;
  box-shadow: var(--shadow-light) !important;
  overflow: hidden !important;
}

.param-cell {
  transition: background-color var(--animation-duration-fast) !important;
}

.param-cell:active {
  background-color: var(--van-active-color) !important;
}

/* ==================== 参数值样式 ==================== */
.param-value-container {
  display: flex;
  align-items: baseline;
  gap: var(--spacing-xs);
}

.param-value {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--van-text-color);
  font-family: 'Courier New', monospace;
}

.param-value--warning {
  color: var(--van-warning-color);
}

.param-unit {
  font-size: var(--font-size-sm);
  color: var(--van-text-color-2);
  font-weight: normal;
}

/* ==================== 空状态 ==================== */
.empty-params {
  padding: var(--spacing-xxl) 0;
}

.coming-soon {
  padding: var(--spacing-xxl) 0;
}

/* ==================== 实时数据动画 ==================== */
.param-value {
  animation: dataUpdate 0.3s ease-in-out;
}

@keyframes dataUpdate {
  0% {
    background-color: rgba(25, 137, 250, 0.1);
  }
  100% {
    background-color: transparent;
  }
}

/* ==================== 参数状态指示器 ==================== */
.param-status {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.param-status::before {
  content: '';
  width: 8rpx;
  height: 8rpx;
  border-radius: 50%;
  background-color: var(--van-success-color);
}

.param-status--warning::before {
  background-color: var(--van-warning-color);
}

.param-status--error::before {
  background-color: var(--van-danger-color);
}

/* ==================== 响应式设计 ==================== */
@media (max-width: 750rpx) {
  .header-section {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-sm);
  }
  
  .tab-content {
    padding: var(--spacing-sm);
  }
  
  .param-value-container {
    flex-direction: column;
    align-items: flex-end;
    gap: 2rpx;
  }
}

/* ==================== 数据刷新提示 ==================== */
.refresh-indicator {
  position: fixed;
  top: 100rpx;
  right: var(--spacing-md);
  background-color: var(--van-primary-color);
  color: white;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-sm);
  z-index: 1000;
  animation: fadeInOut 2s ease-in-out;
}

@keyframes fadeInOut {
  0%, 100% {
    opacity: 0;
    transform: translateX(100%);
  }
  10%, 90% {
    opacity: 1;
    transform: translateX(0);
  }
}
