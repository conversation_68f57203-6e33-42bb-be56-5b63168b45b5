// pages/realtime/data.js
import { getDeviceNormalParams, getDeviceAlertInfo, getDeviceControlParams } from '../../api/device.js'
import { checkPagePermission } from '../../utils/permission.js'
import { showError, showSuccess } from '../../utils/request.js'

Page({

  /**
   * 页面的初始数据
   */
  data: {
    // 设备信息
    deviceId: null,
    deviceName: '',
    
    // 当前选中的标签页
    activeTab: 0,
    
    // 一般参数数据
    normalParams: [],
    normalParamsLoading: true,
    normalParamsError: false,
    
    // 告警参数数据
    alertParams: [],
    alertParamsLoading: false,
    alertParamsError: false,
    
    // 控制参数数据
    controlParams: [],
    controlParamsLoading: false,
    controlParamsError: false,
    
    // 定时器ID
    refreshTimer: null,
    
    // 刷新间隔（毫秒）
    refreshInterval: 1000,
    
    // 页面是否可见
    isPageVisible: true,
    
    // 最后更新时间
    lastUpdateTime: ''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('实时数据页面加载, 参数:', options)
    
    // 检查页面权限
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]
    const currentUrl = '/' + currentPage.route

    if (!checkPagePermission(currentUrl)) {
      return
    }

    // 获取设备信息
    const deviceId = options.id
    const deviceName = decodeURIComponent(options.name || '设备')
    
    if (!deviceId) {
      showError('设备ID不能为空')
      wx.navigateBack()
      return
    }

    this.setData({ 
      deviceId: parseInt(deviceId),
      deviceName: deviceName
    })

    // 更新页面标题
    wx.setNavigationBarTitle({
      title: `${deviceName} - 实时数据`
    })

    // 初始化页面数据
    this.initPageData()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {
    console.log('实时数据页面渲染完成')
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    console.log('实时数据页面显示')
    this.setData({ isPageVisible: true })
    
    // 如果已经有数据，重新开始定时刷新
    if (this.data.normalParams.length > 0) {
      this.startAutoRefresh()
    }
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {
    console.log('实时数据页面隐藏')
    this.setData({ isPageVisible: false })
    this.stopAutoRefresh()
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    console.log('实时数据页面卸载')
    this.stopAutoRefresh()
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    console.log('下拉刷新实时数据')
    this.refreshCurrentTabData().then(() => {
      wx.stopPullDownRefresh()
    })
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: `${this.data.deviceName} - 实时数据`,
      path: `/pages/realtime/data?id=${this.data.deviceId}&name=${encodeURIComponent(this.data.deviceName)}`
    }
  },

  /**
   * 初始化页面数据
   */
  async initPageData() {
    try {
      // 加载一般参数数据
      await this.loadNormalParams()
      
      // 开始自动刷新
      this.startAutoRefresh()
    } catch (error) {
      console.error('初始化页面数据失败:', error)
    }
  },

  /**
   * 标签页切换
   */
  onTabChange(event) {
    const activeTab = event.detail.index
    console.log('切换到标签页:', activeTab)
    
    this.setData({ activeTab })
    
    // 停止当前的自动刷新
    this.stopAutoRefresh()
    
    // 根据标签页加载对应数据
    switch (activeTab) {
      case 0: // 一般参数
        if (this.data.normalParams.length === 0) {
          this.loadNormalParams()
        }
        this.startAutoRefresh()
        break
      case 1: // 告警参数
        if (this.data.alertParams.length === 0) {
          this.loadAlertParams()
        }
        // TODO: 告警参数也需要定时刷新
        break
      case 2: // 控制参数
        if (this.data.controlParams.length === 0) {
          this.loadControlParams()
        }
        // TODO: 控制参数也需要定时刷新
        break
    }
  },

  /**
   * 加载一般参数数据
   */
  async loadNormalParams() {
    if (!this.data.deviceId) return

    this.setData({ 
      normalParamsLoading: true,
      normalParamsError: false
    })

    try {
      console.log('📤 加载一般参数, 设备ID:', this.data.deviceId)
      const result = await getDeviceNormalParams(this.data.deviceId)
      
      console.log('📥 一般参数响应:', result)

      if (result && result.data) {
        // 处理参数数据，转换为数组格式便于显示
        const params = this.processNormalParams(result.data)
        
        this.setData({
          normalParams: params,
          normalParamsLoading: false,
          normalParamsError: false,
          lastUpdateTime: this.formatCurrentTime()
        })
      } else {
        throw new Error('一般参数数据格式错误')
      }
    } catch (error) {
      console.error('❌ 加载一般参数失败:', error)
      this.setData({
        normalParamsLoading: false,
        normalParamsError: true
      })

      // 只在首次加载失败时显示错误提示
      if (this.data.normalParams.length === 0) {
        showError('加载一般参数失败')
      }
    }
  },

  /**
   * 处理一般参数数据
   */
  processNormalParams(data) {
    if (!data || typeof data !== 'object') {
      return []
    }

    const params = []

    // 遍历数据对象，转换为参数数组
    for (const [key, value] of Object.entries(data)) {
      // 跳过一些不需要显示的字段
      if (key === 'id' || key === 'deviceId' || key === 'createTime' || key === 'updateTime') {
        continue
      }

      params.push({
        name: this.getParamDisplayName(key),
        key: key,
        value: value,
        unit: this.getParamUnit(key),
        type: this.getParamType(key, value)
      })
    }

    return params
  },

  /**
   * 获取参数显示名称
   */
  getParamDisplayName(key) {
    const nameMap = {
      'temperature': '温度',
      'humidity': '湿度',
      'pressure': '压力',
      'voltage': '电压',
      'current': '电流',
      'power': '功率',
      'frequency': '频率',
      'speed': '转速',
      'flow': '流量',
      'level': '液位',
      'ph': 'pH值',
      'conductivity': '电导率',
      'turbidity': '浊度',
      'dissolvedOxygen': '溶解氧',
      'cod': 'COD',
      'bod': 'BOD',
      'tss': 'TSS',
      'nh3n': '氨氮',
      'tp': '总磷',
      'tn': '总氮'
    }

    return nameMap[key] || key
  },

  /**
   * 获取参数单位
   */
  getParamUnit(key) {
    const unitMap = {
      'temperature': '°C',
      'humidity': '%RH',
      'pressure': 'Pa',
      'voltage': 'V',
      'current': 'A',
      'power': 'W',
      'frequency': 'Hz',
      'speed': 'rpm',
      'flow': 'L/min',
      'level': 'm',
      'ph': '',
      'conductivity': 'μS/cm',
      'turbidity': 'NTU',
      'dissolvedOxygen': 'mg/L',
      'cod': 'mg/L',
      'bod': 'mg/L',
      'tss': 'mg/L',
      'nh3n': 'mg/L',
      'tp': 'mg/L',
      'tn': 'mg/L'
    }

    return unitMap[key] || ''
  },

  /**
   * 获取参数类型（用于显示不同颜色）
   */
  getParamType(key, value) {
    // 根据参数值判断状态
    if (typeof value === 'number') {
      // 这里可以根据实际业务逻辑设置阈值
      if (key === 'temperature' && (value < 0 || value > 50)) {
        return 'warning'
      }
      if (key === 'humidity' && (value < 30 || value > 80)) {
        return 'warning'
      }
      return 'normal'
    }

    return 'normal'
  },

  /**
   * 加载告警参数数据
   */
  async loadAlertParams() {
    // TODO: 实现告警参数加载
    console.log('加载告警参数 - 待实现')
  },

  /**
   * 加载控制参数数据
   */
  async loadControlParams() {
    // TODO: 实现控制参数加载
    console.log('加载控制参数 - 待实现')
  },

  /**
   * 开始自动刷新
   */
  startAutoRefresh() {
    // 清除之前的定时器
    this.stopAutoRefresh()

    console.log('🔄 开始自动刷新，间隔:', this.data.refreshInterval, 'ms')

    const timer = setInterval(() => {
      // 只有在页面可见且当前是一般参数标签页时才刷新
      if (this.data.isPageVisible && this.data.activeTab === 0) {
        console.log('🔄 自动刷新一般参数')
        this.loadNormalParams()
      }
    }, this.data.refreshInterval)

    this.setData({ refreshTimer: timer })
  },

  /**
   * 停止自动刷新
   */
  stopAutoRefresh() {
    if (this.data.refreshTimer) {
      console.log('⏹️ 停止自动刷新')
      clearInterval(this.data.refreshTimer)
      this.setData({ refreshTimer: null })
    }
  },

  /**
   * 刷新当前标签页数据
   */
  async refreshCurrentTabData() {
    switch (this.data.activeTab) {
      case 0:
        await this.loadNormalParams()
        break
      case 1:
        await this.loadAlertParams()
        break
      case 2:
        await this.loadControlParams()
        break
    }
  },

  /**
   * 格式化当前时间
   */
  formatCurrentTime() {
    const now = new Date()
    return now.toLocaleTimeString('zh-CN')
  },

  /**
   * 重试加载
   */
  onRetryLoad() {
    switch (this.data.activeTab) {
      case 0:
        this.loadNormalParams()
        break
      case 1:
        this.loadAlertParams()
        break
      case 2:
        this.loadControlParams()
        break
    }
  }
})
