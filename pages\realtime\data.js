// pages/realtime/data.js
import { getDeviceNormalParams, getDeviceAlertInfo, getDeviceControlParams } from '../../api/device.js'
import { checkPagePermission } from '../../utils/permission.js'
import { showError, showSuccess } from '../../utils/request.js'

Page({

  /**
   * 页面的初始数据
   */
  data: {
    // 设备信息
    deviceId: null,
    deviceName: '',
    
    // 当前选中的标签页
    activeTab: 0,
    
    // 一般参数数据
    normalParams: [],
    normalParamsLoading: false,
    normalParamsError: false,
    
    // 告警参数数据
    alertParams: [],
    alertParamsLoading: false,
    alertParamsError: false,
    
    // 控制参数数据
    controlParams: [],
    controlParamsLoading: false,
    controlParamsError: false,
    
    // 定时器ID
    refreshTimer: null,
    
    // 刷新间隔（毫秒）
    refreshInterval: 1000,
    
    // 页面是否可见
    isPageVisible: true,
    
    // 最后更新时间
    lastUpdateTime: ''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('实时数据页面加载, 参数:', options)
    
    // 检查页面权限
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]
    const currentUrl = '/' + currentPage.route

    if (!checkPagePermission(currentUrl)) {
      return
    }

    // 获取设备信息
    const deviceId = options.id
    const deviceName = decodeURIComponent(options.name || '设备')
    
    if (!deviceId) {
      showError('设备ID不能为空')
      wx.navigateBack()
      return
    }

    this.setData({ 
      deviceId: parseInt(deviceId),
      deviceName: deviceName
    })

    // 更新页面标题
    wx.setNavigationBarTitle({
      title: `${deviceName} - 实时数据`
    })

    // 初始化页面数据
    this.initPageData()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {
    console.log('实时数据页面渲染完成')
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    console.log('实时数据页面显示')
    this.setData({ isPageVisible: true })
    
    // 如果已经有数据，重新开始定时刷新
    if (this.data.normalParams.length > 0) {
      this.startAutoRefresh()
    }
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {
    console.log('实时数据页面隐藏')
    this.setData({ isPageVisible: false })
    this.stopAutoRefresh()
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    console.log('实时数据页面卸载')
    this.stopAutoRefresh()
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    console.log('下拉刷新实时数据')
    this.refreshCurrentTabData().then(() => {
      wx.stopPullDownRefresh()
    }).catch(() => {
      // 静默处理刷新失败
      wx.stopPullDownRefresh()
    })
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: `${this.data.deviceName} - 实时数据`,
      path: `/pages/realtime/data?id=${this.data.deviceId}&name=${encodeURIComponent(this.data.deviceName)}`
    }
  },

  /**
   * 初始化页面数据
   */
  async initPageData() {
    try {
      // 加载一般参数数据
      await this.loadNormalParams()

      // 开始自动刷新
      this.startAutoRefresh()
    } catch (error) {
      console.error('初始化页面数据失败:', error)
      // 静默处理初始化失败，不显示任何提示
      // 仍然开始自动刷新，让后续请求有机会成功
      this.startAutoRefresh()
    }
  },

  /**
   * 标签页切换
   */
  onTabChange(event) {
    const activeTab = event.detail.index
    console.log('切换到标签页:', activeTab)
    
    this.setData({ activeTab })
    
    // 停止当前的自动刷新
    this.stopAutoRefresh()
    
    // 根据标签页加载对应数据
    switch (activeTab) {
      case 0: // 一般参数
        if (this.data.normalParams.length === 0) {
          this.loadNormalParams()
        }
        this.startAutoRefresh()
        break
      case 1: // 告警参数
        if (this.data.alertParams.length === 0) {
          this.loadAlertParams()
        }
        // TODO: 告警参数也需要定时刷新
        break
      case 2: // 控制参数
        if (this.data.controlParams.length === 0) {
          this.loadControlParams()
        }
        // TODO: 控制参数也需要定时刷新
        break
    }
  },

  /**
   * 加载一般参数数据
   */
  async loadNormalParams() {
    if (!this.data.deviceId) return

    // 只重置错误状态，不显示加载状态
    this.setData({
      normalParamsError: false
    })

    try {
      console.log('📤 加载一般参数, 设备ID:', this.data.deviceId)
      const result = await getDeviceNormalParams(this.data.deviceId)
      
      console.log('📥 一般参数响应:', result)

      // 处理不同的响应格式
      let paramsData = null

      if (result && result.data && result.data.rows) {
        // RuoYi格式: {code: 200, data: {rows: [...], total: xx}}
        paramsData = result.data.rows
        console.log('✅ RuoYi格式响应，参数数量:', paramsData.length)
      } else if (result && result.rows) {
        // 直接分页格式: {rows: [...], total: xx}
        paramsData = result.rows
        console.log('✅ 直接分页格式响应，参数数量:', paramsData.length)
      } else if (result && result.data && Array.isArray(result.data)) {
        // 直接数组格式: {code: 200, data: [...]}
        paramsData = result.data
        console.log('✅ 直接数组格式响应，参数数量:', paramsData.length)
      } else if (result && Array.isArray(result)) {
        // 纯数组格式: [...]
        paramsData = result
        console.log('✅ 纯数组格式响应，参数数量:', paramsData.length)
      } else {
        console.error('❌ 未知的响应格式:', result)
        throw new Error('一般参数数据格式错误')
      }

      if (paramsData && Array.isArray(paramsData)) {
        // 处理参数数据，转换为显示格式
        const params = this.processNormalParams(paramsData)

        this.setData({
          normalParams: params,
          normalParamsError: false,
          lastUpdateTime: this.formatCurrentTime()
        })
      } else {
        throw new Error('参数数据不是有效的数组格式')
      }
    } catch (error) {
      console.error('❌ 加载一般参数失败:', error)

      // 静默处理错误，不显示任何提示
      // 只在控制台记录错误，用户界面保持不变

      // 如果是首次加载失败，设置错误状态以显示重试按钮
      if (this.data.normalParams.length === 0) {
        this.setData({
          normalParamsError: true
        })
      }
      // 如果已有数据，则保持现有数据不变，静默失败
    }
  },

  /**
   * 处理一般参数数据
   */
  processNormalParams(data) {
    if (!data || !Array.isArray(data)) {
      console.error('❌ 参数数据不是数组格式:', data)
      return []
    }

    console.log('🔄 处理参数数据，数量:', data.length)

    const params = []

    // 遍历参数数组
    data.forEach((item, index) => {
      console.log(`📋 处理参数 ${index + 1}:`, item)

      if (item && typeof item === 'object') {
        // 解析参数值和单位
        const { value, unit } = this.parseParamValue(item.paramValue || '')

        const param = {
          id: item.paramId,
          name: item.paramName || `参数${item.paramId}`,
          key: item.paramKey || `param_${item.paramId}`,
          value: value,
          unit: unit,
          originalValue: item.paramValue,
          alertValue: item.alertValue,
          rwType: item.rwType,
          paramType: item.paramType,
          type: this.getParamStatusType(item.alertValue, value)
        }

        params.push(param)
        console.log(`✅ 参数处理完成:`, param)
      }
    })

    console.log('🎯 最终处理的参数列表:', params)
    return params
  },

  /**
   * 解析参数值和单位
   */
  parseParamValue(paramValue) {
    if (!paramValue || typeof paramValue !== 'string') {
      return { value: paramValue || '--', unit: '' }
    }

    // 使用正则表达式分离数值和单位
    const match = paramValue.match(/^([\d.-]+)\s*(.*)$/)

    if (match) {
      const numericValue = parseFloat(match[1])
      const unit = match[2].trim()

      return {
        value: isNaN(numericValue) ? match[1] : numericValue,
        unit: unit
      }
    }

    // 如果无法解析，返回原始值
    return { value: paramValue, unit: '' }
  },

  /**
   * 获取参数状态类型
   */
  getParamStatusType(alertValue, value) {
    // 如果有告警标识
    if (alertValue === true || alertValue === 1) {
      return 'warning'
    }

    // 根据数值判断（可以根据实际业务逻辑调整）
    if (typeof value === 'number') {
      // 这里可以添加更多的业务逻辑判断
      return 'normal'
    }

    return 'normal'
  },



  /**
   * 加载告警参数数据
   */
  async loadAlertParams() {
    // TODO: 实现告警参数加载
    console.log('加载告警参数 - 待实现')
  },

  /**
   * 加载控制参数数据
   */
  async loadControlParams() {
    // TODO: 实现控制参数加载
    console.log('加载控制参数 - 待实现')
  },

  /**
   * 开始自动刷新
   */
  startAutoRefresh() {
    // 清除之前的定时器
    this.stopAutoRefresh()

    console.log('🔄 开始自动刷新，间隔:', this.data.refreshInterval, 'ms')

    const timer = setInterval(() => {
      // 只有在页面可见且当前是一般参数标签页时才刷新
      if (this.data.isPageVisible && this.data.activeTab === 0) {
        console.log('🔄 自动刷新一般参数')
        this.loadNormalParams()
      }
    }, this.data.refreshInterval)

    this.setData({ refreshTimer: timer })
  },

  /**
   * 停止自动刷新
   */
  stopAutoRefresh() {
    if (this.data.refreshTimer) {
      console.log('⏹️ 停止自动刷新')
      clearInterval(this.data.refreshTimer)
      this.setData({ refreshTimer: null })
    }
  },

  /**
   * 刷新当前标签页数据
   */
  async refreshCurrentTabData() {
    switch (this.data.activeTab) {
      case 0:
        await this.loadNormalParams()
        break
      case 1:
        await this.loadAlertParams()
        break
      case 2:
        await this.loadControlParams()
        break
    }
  },

  /**
   * 格式化当前时间
   */
  formatCurrentTime() {
    const now = new Date()
    return now.toLocaleTimeString('zh-CN')
  },

  /**
   * 重试加载
   */
  onRetryLoad() {
    switch (this.data.activeTab) {
      case 0:
        this.loadNormalParams()
        break
      case 1:
        this.loadAlertParams()
        break
      case 2:
        this.loadControlParams()
        break
    }
  }
})
