<!--pages/realtime/data.wxml-->
<view class="realtime-data-page">

  <!-- 页面头部信息 -->
  <view class="header-section">
    <view class="device-info">
      <text class="device-name">{{deviceName}}</text>
      <text class="device-id">ID: {{deviceId}}</text>
    </view>
    <view wx:if="{{lastUpdateTime}}" class="update-time">
      <van-icon name="clock-o" size="12" />
      <text class="time-text">{{lastUpdateTime}}</text>
    </view>
  </view>

  <!-- 实时数据刷新提示 -->
  <van-notice-bar
    text="数据每秒自动刷新，确保信息实时性"
    mode="closeable"
    background="#e1f3ff"
    color="#1989fa"
    left-icon="info-o"
  />

  <!-- 标签页 -->
  <van-tabs 
    active="{{activeTab}}" 
    bind:change="onTabChange"
    sticky
    offset-top="0"
    color="#1989fa"
    title-active-color="#1989fa"
    title-inactive-color="#969799"
  >
    
    <!-- 一般参数标签页 -->
    <van-tab title="一般参数" name="normal">
      <view class="tab-content">
        
        <!-- 加载状态 -->
        <view wx:if="{{normalParamsLoading}}" class="loading-container">
          <van-loading type="spinner" size="24" text-size="14">加载中...</van-loading>
        </view>

        <!-- 错误状态 -->
        <view wx:elif="{{normalParamsError}}" class="error-container">
          <van-empty
            image="error"
            description="加载失败"
          >
            <van-button
              round
              type="primary"
              size="small"
              bind:click="onRetryLoad"
            >
              重新加载
            </van-button>
          </van-empty>
        </view>

        <!-- 参数列表 -->
        <view wx:else class="params-list">
          <van-cell-group wx:if="{{normalParams.length > 0}}" custom-class="params-group">
            <van-cell
              wx:for="{{normalParams}}"
              wx:key="key"
              title="{{item.name}}"
              custom-class="param-cell"
            >
              <view slot="right-icon" class="param-value-container">
                <text class="param-value {{item.type === 'warning' ? 'param-value--warning' : ''}}">
                  {{item.value}}
                </text>
                <text wx:if="{{item.unit}}" class="param-unit">{{item.unit}}</text>
              </view>
            </van-cell>
          </van-cell-group>

          <!-- 空状态 -->
          <view wx:else class="empty-params">
            <van-empty
              image="search"
              description="暂无参数数据"
            />
          </view>
        </view>

      </view>
    </van-tab>

    <!-- 告警参数标签页 -->
    <van-tab title="告警参数" name="alert">
      <view class="tab-content">
        <view class="coming-soon">
          <van-empty
            image="default"
            description="告警参数功能开发中"
          />
        </view>
      </view>
    </van-tab>

    <!-- 控制参数标签页 -->
    <van-tab title="控制参数" name="control">
      <view class="tab-content">
        <view class="coming-soon">
          <van-empty
            image="default"
            description="控制参数功能开发中"
          />
        </view>
      </view>
    </van-tab>

  </van-tabs>

</view>
